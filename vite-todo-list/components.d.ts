/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
// biome-ignore lint: disable
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    AppContent: typeof import('./src/components/AppContent.vue')['default']
    Calendar: typeof import('./src/components/common/Calendar.vue')['default']
    DietPlanningForm: typeof import('./src/components/events/DietPlanningForm.vue')['default']
    GeneralEventForm: typeof import('./src/components/events/GeneralEventForm.vue')['default']
    GlobalProviders: typeof import('./src/components/GlobalProviders.vue')['default']
    HelloWorld: typeof import('./src/components/HelloWorld.vue')['default']
    NAlert: typeof import('naive-ui')['NAlert']
    NAvatar: typeof import('naive-ui')['NAvatar']
    NButton: typeof import('naive-ui')['NButton']
    NCard: typeof import('naive-ui')['NCard']
    NCheckbox: typeof import('naive-ui')['NCheckbox']
    NCollapse: typeof import('naive-ui')['NCollapse']
    NCollapseItem: typeof import('naive-ui')['NCollapseItem']
    NConfigProvider: typeof import('naive-ui')['NConfigProvider']
    NDatePicker: typeof import('naive-ui')['NDatePicker']
    NDialogProvider: typeof import('naive-ui')['NDialogProvider']
    NDivider: typeof import('naive-ui')['NDivider']
    NDropdown: typeof import('naive-ui')['NDropdown']
    NForm: typeof import('naive-ui')['NForm']
    NFormItem: typeof import('naive-ui')['NFormItem']
    NGlobalStyle: typeof import('naive-ui')['NGlobalStyle']
    NGrid: typeof import('naive-ui')['NGrid']
    NGridItem: typeof import('naive-ui')['NGridItem']
    NIcon: typeof import('naive-ui')['NIcon']
    NInput: typeof import('naive-ui')['NInput']
    NInputNumber: typeof import('naive-ui')['NInputNumber']
    NLoadingBarProvider: typeof import('naive-ui')['NLoadingBarProvider']
    NMenu: typeof import('naive-ui')['NMenu']
    NMessageProvider: typeof import('naive-ui')['NMessageProvider']
    NModal: typeof import('naive-ui')['NModal']
    NNotificationProvider: typeof import('naive-ui')['NNotificationProvider']
    NPopconfirm: typeof import('naive-ui')['NPopconfirm']
    NProgress: typeof import('naive-ui')['NProgress']
    NRadio: typeof import('naive-ui')['NRadio']
    NRadioGroup: typeof import('naive-ui')['NRadioGroup']
    NSelect: typeof import('naive-ui')['NSelect']
    NSpace: typeof import('naive-ui')['NSpace']
    NSpin: typeof import('naive-ui')['NSpin']
    NSwitch: typeof import('naive-ui')['NSwitch']
    NTag: typeof import('naive-ui')['NTag']
    NTimePicker: typeof import('naive-ui')['NTimePicker']
    NUpload: typeof import('naive-ui')['NUpload']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    TestInfo: typeof import('./src/components/TestInfo.vue')['default']
  }
}
