# 待办日历应用

一个功能完整的待办事项和日历管理应用，支持移动端和桌面端，基于 Vue 3 + TypeScript + NaiveUI 构建。

## 功能特性

### 📱 响应式设计
- 自动检测设备类型（移动端/桌面端）
- 移动端：底部导航栏 + 垂直布局
- 桌面端：侧边栏 + 网格布局

### 📅 日历功能
- 集成 VCalendar 组件
- 事件状态颜色标识
- 日期选择和导航
- 月视图展示

### 🎯 事件管理
- 多种事件类型支持
- 动态表单配置
- 事件创建、编辑、删除
- 完成状态管理
- 优先级设置

### 🍽️ 饮食规划
- 预设饮食类型（地中海、DASH、植物性等）
- 餐次管理（早餐、午餐、晚餐、加餐）
- 营养信息展示
- 卡路里计划

### 📝 通用事件
- 记事本功能
- 标签管理
- 文件附件
- 提醒设置

### 👤 用户系统
- 用户注册/登录
- 个人资料管理
- 密码修改
- 账户设置

## 技术栈

- **前端框架**: Vue 3 + TypeScript
- **UI 组件库**: NaiveUI
- **日历组件**: VCalendar
- **状态管理**: Pinia
- **路由**: Vue Router 4
- **构建工具**: Vite
- **工具库**: @vueuse/core, axios
- **数据库**: MySQL（设计完成）

## 项目结构

```
src/
├── api/                 # API 接口
├── assets/             # 静态资源
├── components/         # 组件
│   ├── common/        # 通用组件
│   ├── mobile/        # 移动端组件
│   └── desktop/       # 桌面端组件
├── composables/       # 组合式函数
├── layouts/           # 布局组件
├── pages/             # 页面组件
│   ├── auth/         # 认证页面
│   ├── events/       # 事件页面
│   └── settings/     # 设置页面
├── router/            # 路由配置
├── stores/            # 状态管理
├── types/             # 类型定义
└── utils/             # 工具函数
```

## 开发指南

### 环境要求
- Node.js >= 16
- pnpm >= 7

### 安装依赖
```bash
pnpm install
```

### 启动开发服务器
```bash
pnpm dev
```

访问 http://localhost:7002

### 构建生产版本
```bash
pnpm build
```

### 预览生产版本
```bash
pnpm preview
```

## 数据库设计

项目包含完整的 MySQL 数据库设计：

- `database/schema.sql` - 数据库结构
- `database/initial_data.sql` - 初始数据

主要数据表：
- `users` - 用户表
- `event_types` - 事件类型表
- `events` - 事件表
- `diet_types` - 饮食类型表
- `diet_events` - 饮食事件详情表
- `event_completions` - 事件完成记录表

## 配置说明

### Vite 配置特性
- 端口: 7002
- 外部访问: 允许
- 代理: API 请求代理到后端
- 自动导入: Vue、Vue Router、Pinia 等
- 组件自动导入: NaiveUI 组件
- 打包优化: 代码分块

### 移动端支持
- 允许的主机: wx.fyg.cn
- 响应式断点: 768px
- 触摸友好的交互设计

## 开发规范

### 代码风格
- 使用 TypeScript 严格模式
- 组合式 API (Composition API)
- 单文件组件 (SFC)
- 响应式设计优先

### 组件规范
- 移动端和桌面端分离
- 通用组件复用
- Props 和 Emits 类型定义
- 样式作用域隔离

## 部署说明

1. 构建项目: `pnpm build`
2. 部署 `dist` 目录到 Web 服务器
3. 配置后端 API 服务
4. 设置数据库连接
5. 导入数据库结构和初始数据

## 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 许可证

MIT License
