-- 初始数据插入
USE todo_calendar;

-- 插入饮食类型数据
INSERT INTO diet_types (name, name_en, description, food_types, nutrition_info, characteristics, benefits, suitable_people, suggested_frequency, suggested_interval, suggested_frequency_per_week, cycle_description, is_system) VALUES
('地中海饮食', 'Mediterranean Diet', '以蔬菜、水果、全谷物、豆类、坚果、橄榄油、鱼类和适量红酒为主，减少红肉和加工食品摄入的健康饮食模式',
 '["橄榄油", "鱼类", "坚果", "全谷物", "蔬菜", "水果", "豆类", "适量红酒"]',
 '{"omega3": "丰富", "纤维": "高", "抗氧化物": "丰富", "饱和脂肪": "低", "单不饱和脂肪": "高"}',
 '以蔬菜、水果、全谷物、豆类、坚果、橄榄油、鱼类和适量红酒为主，减少红肉和加工食品摄入。',
 '对心血管健康有益，降低心脏病、中风和某些癌症风险，改善认知功能。',
 '追求长期健康、喜欢清淡口味的人群。',
 'weekly', 1, 5, '建议每周5天采用地中海饮食，周末可适当放松', TRUE),

('DASH饮食', 'DASH Diet', '膳食方式预防高血压，强调低钠、高钾、镁、钙的食物',
 '["低脂乳制品", "全谷物", "蔬菜", "水果", "瘦肉", "鱼类", "坚果", "豆类"]',
 '{"钠": "低", "钾": "高", "钙": "高", "镁": "高", "纤维": "高"}',
 '强调低钠、高钾、镁、钙的食物，如蔬菜、水果、全谷物、低脂乳制品、瘦肉和鱼类，限制高脂肪和高糖食品。',
 '有效降低血压，改善心血管健康，适合控制体重。',
 '高血压患者或有心血管疾病风险的人。',
 'weekly', 1, 6, '建议每周6天严格遵循DASH饮食原则，每周休息1天', TRUE),

('生酮饮食', 'Ketogenic Diet', '极低碳水化合物、高脂肪的饮食方式，促使身体进入酮症状态',
 '["肉类", "鱼类", "蛋类", "奶酪", "坚果", "种子", "低碳蔬菜", "健康油脂"]',
 '{"碳水化合物": "极低(5-10%)", "脂肪": "高(70-80%)", "蛋白质": "中等(15-25%)"}',
 '严格限制碳水化合物摄入，大幅增加脂肪比例，适量蛋白质。',
 '快速减重，改善血糖控制，可能提高认知功能。',
 '需要快速减重的人群，糖尿病患者（需医生指导）。',
 'monthly', 1, 2, '建议每周进行2次生酮饮食，其他时间正常饮食，需医生指导', TRUE),

('间歇性断食', 'Intermittent Fasting', '通过控制进食时间窗口来调节新陈代谢的饮食方式',
 '["进食窗口内正常饮食", "断食期间只喝水、茶、黑咖啡"]',
 '{"热量控制": "自然限制", "胰岛素敏感性": "提高", "代谢灵活性": "增强"}',
 '16:8、18:6或5:2等不同模式，在特定时间窗口内进食。',
 '体重管理，改善胰岛素敏感性，可能延缓衰老。',
 '健康成年人，需要体重管理的人群。',
 'weekly', 1, 5, '建议每周5天进行16:8间歇性断食，周末可正常饮食', TRUE),

('传统中式饮食', 'Traditional Chinese Diet', '基于中医理论的传统中式饮食，讲究食物搭配和药食同源',
 '["谷物", "蔬菜", "豆类", "少量肉类", "中药材", "茶类"]',
 '{"平衡": "阴阳调和", "五味": "酸甜苦辣咸", "温热寒凉": "因人而异"}',
 '讲究食物搭配，注重营养平衡和药食同源。',
 '调理体质，预防疾病，促进消化。',
 '注重养生的人群，体质需要调理的人群。',
 'weekly', 1, 4, '建议每周4天采用传统中式饮食，其他时间可适当调整', TRUE);

-- 插入事件类型数据
INSERT INTO event_types (name, name_en, description, icon, color, form_config, is_system) VALUES
('饮食规划', 'Diet Planning', '记录和规划日常饮食', 'restaurant', '#4CAF50', 
 '{"fields": [{"name": "diet_type", "type": "select", "label": "饮食类型", "required": true}, {"name": "meal_type", "type": "select", "label": "餐次", "options": ["breakfast", "lunch", "dinner", "snack"], "required": true}, {"name": "planned_foods", "type": "textarea", "label": "计划食物"}, {"name": "calories_planned", "type": "number", "label": "计划卡路里"}]}', 
 TRUE),

('通用事件', 'General Event', '通用的记事本功能', 'create', '#2196F3', 
 '{"fields": [{"name": "content", "type": "textarea", "label": "内容", "required": true}, {"name": "priority", "type": "select", "label": "优先级", "options": ["low", "medium", "high"], "default": "medium"}]}', 
 TRUE),

('运动健身', 'Exercise', '运动和健身活动记录', 'fitness', '#FF9800', 
 '{"fields": [{"name": "exercise_type", "type": "text", "label": "运动类型"}, {"name": "duration", "type": "number", "label": "持续时间(分钟)"}, {"name": "intensity", "type": "select", "label": "强度", "options": ["low", "medium", "high"]}]}', 
 TRUE),

('学习计划', 'Study Plan', '学习和教育相关活动', 'school', '#9C27B0', 
 '{"fields": [{"name": "subject", "type": "text", "label": "学习科目"}, {"name": "study_method", "type": "select", "label": "学习方式", "options": ["reading", "practice", "video", "discussion"]}, {"name": "target", "type": "textarea", "label": "学习目标"}]}', 
 TRUE),

('工作任务', 'Work Task', '工作相关的任务和项目', 'briefcase', '#607D8B', 
 '{"fields": [{"name": "project", "type": "text", "label": "项目名称"}, {"name": "task_type", "type": "select", "label": "任务类型", "options": ["meeting", "development", "review", "planning"]}, {"name": "deadline", "type": "datetime", "label": "截止时间"}]}', 
 TRUE);
