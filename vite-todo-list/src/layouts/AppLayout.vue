<template>
  <div class="app-layout">
    <!-- 移动端布局 -->
    <MobileLayout v-if="appStore.isMobile" />
    
    <!-- 桌面端布局 -->
    <DesktopLayout v-else />
  </div>
</template>

<script setup lang="ts">
import { useAppStore } from '@/stores/app'
import MobileLayout from './MobileLayout.vue'
import DesktopLayout from './DesktopLayout.vue'

const appStore = useAppStore()

// 初始化应用
onMounted(() => {
  appStore.init()
})

onUnmounted(() => {
  appStore.destroy()
})
</script>

<style scoped>
.app-layout {
  height: 100vh;
  overflow: hidden;
}
</style>
