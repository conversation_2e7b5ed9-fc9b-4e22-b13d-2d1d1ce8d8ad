<template>
  <div class="general-event-form">
    <n-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-placement="top"
    >
      <n-form-item path="content" label="内容">
        <n-input
          v-model:value="form.content"
          type="textarea"
          placeholder="请输入事件内容..."
          :rows="6"
          show-count
          :maxlength="1000"
        />
      </n-form-item>
      
      <n-form-item path="priority" label="优先级">
        <n-select
          v-model:value="form.priority"
          placeholder="选择优先级"
          :options="priorityOptions"
        />
      </n-form-item>
      
      <n-form-item path="tags" label="标签">
        <n-dynamic-tags
          v-model:value="form.tags"
          placeholder="添加标签"
        />
      </n-form-item>
      
      <n-form-item path="attachments" label="附件">
        <n-upload
          v-model:file-list="form.attachments"
          multiple
          directory-dnd
          :max="5"
          :on-before-upload="handleBeforeUpload"
        >
          <n-upload-dragger>
            <div style="margin-bottom: 12px">
              <n-icon size="48" :depth="3">
                <CloudUpload />
              </n-icon>
            </div>
            <n-text style="font-size: 16px">
              点击或者拖动文件到该区域来上传
            </n-text>
            <n-p depth="3" style="margin: 8px 0 0 0">
              支持单个或批量上传，最多5个文件
            </n-p>
          </n-upload-dragger>
        </n-upload>
      </n-form-item>
      
      <n-form-item path="reminder" label="提醒设置">
        <n-space vertical>
          <n-checkbox v-model:checked="form.reminder.enabled">
            启用提醒
          </n-checkbox>
          
          <div v-if="form.reminder.enabled" class="reminder-settings">
            <n-space>
              <n-input-number
                v-model:value="form.reminder.minutes"
                placeholder="分钟"
                :min="1"
                :max="1440"
                style="width: 120px"
              />
              <span>分钟前提醒</span>
            </n-space>
            
            <n-checkbox-group v-model:value="form.reminder.methods" style="margin-top: 8px">
              <n-space>
                <n-checkbox value="notification" label="浏览器通知" />
                <n-checkbox value="email" label="邮件提醒" />
                <n-checkbox value="sms" label="短信提醒" />
              </n-space>
            </n-checkbox-group>
          </div>
        </n-space>
      </n-form-item>
    </n-form>
  </div>
</template>

<script setup lang="ts">
import { CloudUpload } from '@vicons/ionicons5'
import type { FormInst, FormRules, UploadFileInfo } from 'naive-ui'

interface GeneralEventData {
  content: string
  priority: 'low' | 'medium' | 'high'
  tags: string[]
  attachments: UploadFileInfo[]
  reminder: {
    enabled: boolean
    minutes: number
    methods: string[]
  }
}

interface Props {
  modelValue?: GeneralEventData
}

interface Emits {
  (e: 'update:modelValue', value: GeneralEventData): void
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: () => ({
    content: '',
    priority: 'medium' as const,
    tags: [],
    attachments: [],
    reminder: {
      enabled: false,
      minutes: 15,
      methods: []
    }
  })
})

const emit = defineEmits<Emits>()

// 表单引用
const formRef = ref<FormInst | null>(null)

// 表单数据
const form = reactive<GeneralEventData>({
  content: props.modelValue.content,
  priority: props.modelValue.priority,
  tags: [...(props.modelValue.tags || [])],
  attachments: [...(props.modelValue.attachments || [])],
  reminder: {
    enabled: props.modelValue.reminder?.enabled || false,
    minutes: props.modelValue.reminder?.minutes || 15,
    methods: [...(props.modelValue.reminder?.methods || [])]
  }
})

// 优先级选项
const priorityOptions = [
  { label: '低', value: 'low' },
  { label: '中', value: 'medium' },
  { label: '高', value: 'high' }
]

// 表单验证规则
const rules: FormRules = {
  content: [
    { required: true, message: '请输入事件内容', trigger: 'blur' },
    { min: 1, max: 1000, message: '内容长度应在1-1000字符之间', trigger: 'blur' }
  ]
}

// 处理文件上传前的验证
const handleBeforeUpload = (data: { file: UploadFileInfo; fileList: UploadFileInfo[] }) => {
  const { file } = data
  
  // 检查文件大小（限制为10MB）
  if (file.file && file.file.size > 10 * 1024 * 1024) {
    window.$message?.error('文件大小不能超过10MB')
    return false
  }
  
  // 检查文件类型
  const allowedTypes = [
    'image/jpeg',
    'image/png',
    'image/gif',
    'application/pdf',
    'text/plain',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
  ]
  
  if (file.file && !allowedTypes.includes(file.file.type)) {
    window.$message?.error('不支持的文件类型')
    return false
  }
  
  return true
}

// 监听表单变化并向上传递
watch(form, (newValue) => {
  emit('update:modelValue', { ...newValue })
}, { deep: true })

// 监听props变化
watch(() => props.modelValue, (newValue) => {
  if (newValue) {
    Object.assign(form, {
      ...newValue,
      tags: [...(newValue.tags || [])],
      attachments: [...(newValue.attachments || [])],
      reminder: {
        ...newValue.reminder,
        methods: [...(newValue.reminder?.methods || [])]
      }
    })
  }
}, { deep: true })

// 暴露验证方法
defineExpose({
  validate: () => formRef.value?.validate()
})
</script>

<style scoped>
.general-event-form {
  width: 100%;
}

.reminder-settings {
  padding: 12px;
  background: #f9f9f9;
  border-radius: 6px;
  border: 1px solid #e0e0e0;
}

@media (max-width: 768px) {
  .reminder-settings {
    padding: 8px;
  }
}
</style>
