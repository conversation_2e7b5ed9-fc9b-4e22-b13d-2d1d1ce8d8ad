<template>
  <div class="diet-planning-form">
    <n-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-placement="top"
    >
      <n-form-item path="diet_type_id" label="饮食类型">
        <n-select
          v-model:value="form.diet_type_id"
          placeholder="选择饮食类型"
          :options="dietTypeOptions"
          @update:value="handleDietTypeChange"
        />
      </n-form-item>
      
      <n-form-item path="meal_type" label="餐次">
        <n-select
          v-model:value="form.meal_type"
          placeholder="选择餐次"
          :options="mealTypeOptions"
        />
      </n-form-item>
      
      <n-form-item path="planned_foods" label="计划食物">
        <n-dynamic-tags
          v-model:value="form.planned_foods"
          placeholder="添加计划食物"
        />
      </n-form-item>
      
      <n-form-item path="calories_planned" label="计划卡路里">
        <n-input-number
          v-model:value="form.calories_planned"
          placeholder="输入计划卡路里"
          :min="0"
          style="width: 100%"
        />
      </n-form-item>
      
      <n-form-item path="notes" label="备注">
        <n-input
          v-model:value="form.notes"
          type="textarea"
          placeholder="添加备注信息"
          :rows="3"
        />
      </n-form-item>
      
      <!-- 饮食类型详细信息展示 -->
      <div v-if="selectedDietType" class="diet-type-info">
        <n-card title="饮食类型信息" size="small">
          <div class="diet-info-grid">
            <div class="info-item">
              <label>描述</label>
              <p>{{ selectedDietType.description }}</p>
            </div>
            
            <div class="info-item">
              <label>包含食物类型</label>
              <n-space>
                <n-tag
                  v-for="food in selectedDietType.food_types"
                  :key="food"
                  type="info"
                  size="small"
                >
                  {{ food }}
                </n-tag>
              </n-space>
            </div>
            
            <div class="info-item">
              <label>营养特点</label>
              <div class="nutrition-info">
                <n-space>
                  <n-tag
                    v-for="(value, key) in selectedDietType.nutrition_info"
                    :key="key"
                    type="success"
                    size="small"
                  >
                    {{ key }}: {{ value }}
                  </n-tag>
                </n-space>
              </div>
            </div>
            
            <div class="info-item">
              <label>特点</label>
              <p>{{ selectedDietType.characteristics }}</p>
            </div>
            
            <div class="info-item">
              <label>益处</label>
              <p>{{ selectedDietType.benefits }}</p>
            </div>
            
            <div class="info-item">
              <label>适合人群</label>
              <p>{{ selectedDietType.suitable_people }}</p>
            </div>
          </div>
        </n-card>
      </div>
    </n-form>
  </div>
</template>

<script setup lang="ts">
import type { FormInst, FormRules } from 'naive-ui'
import { useEventsStore } from '@/stores/events'
import type { DietType } from '@/types'

interface DietPlanningData {
  diet_type_id?: number
  meal_type: 'breakfast' | 'lunch' | 'dinner' | 'snack'
  planned_foods: string[]
  calories_planned?: number
  notes?: string
}

interface Props {
  modelValue?: DietPlanningData
}

interface Emits {
  (e: 'update:modelValue', value: DietPlanningData): void
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: () => ({
    meal_type: 'breakfast' as const,
    planned_foods: [],
    calories_planned: undefined,
    notes: ''
  })
})

const emit = defineEmits<Emits>()

const eventsStore = useEventsStore()

// 表单引用
const formRef = ref<FormInst | null>(null)

// 表单数据
const form = reactive<DietPlanningData>({
  diet_type_id: props.modelValue.diet_type_id,
  meal_type: props.modelValue.meal_type,
  planned_foods: [...(props.modelValue.planned_foods || [])],
  calories_planned: props.modelValue.calories_planned,
  notes: props.modelValue.notes || ''
})

// 选中的饮食类型
const selectedDietType = ref<DietType | null>(null)

// 饮食类型选项
const dietTypeOptions = computed(() => {
  return eventsStore.dietTypes.map(type => ({
    label: type.name,
    value: type.id
  }))
})

// 餐次选项
const mealTypeOptions = [
  { label: '早餐', value: 'breakfast' },
  { label: '午餐', value: 'lunch' },
  { label: '晚餐', value: 'dinner' },
  { label: '加餐', value: 'snack' }
]

// 表单验证规则
const rules: FormRules = {
  meal_type: [
    { required: true, message: '请选择餐次', trigger: 'change' }
  ]
}

// 处理饮食类型变化
const handleDietTypeChange = (value: number) => {
  selectedDietType.value = eventsStore.getDietType(value) || null
}

// 监听表单变化并向上传递
watch(form, (newValue) => {
  emit('update:modelValue', { ...newValue })
}, { deep: true })

// 监听props变化
watch(() => props.modelValue, (newValue) => {
  if (newValue) {
    Object.assign(form, newValue)
    if (newValue.diet_type_id) {
      selectedDietType.value = eventsStore.getDietType(newValue.diet_type_id) || null
    }
  }
}, { deep: true })

// 暴露验证方法
defineExpose({
  validate: () => formRef.value?.validate()
})

// 初始化
onMounted(() => {
  if (form.diet_type_id) {
    selectedDietType.value = eventsStore.getDietType(form.diet_type_id) || null
  }
})
</script>

<style scoped>
.diet-planning-form {
  width: 100%;
}

.diet-type-info {
  margin-top: 24px;
}

.diet-info-grid {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.info-item label {
  font-size: 14px;
  font-weight: 600;
  color: #333;
}

.info-item p {
  font-size: 14px;
  color: #666;
  line-height: 1.5;
  margin: 0;
}

.nutrition-info {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

@media (max-width: 768px) {
  .diet-info-grid {
    gap: 12px;
  }
  
  .info-item label {
    font-size: 13px;
  }
  
  .info-item p {
    font-size: 13px;
  }
}
</style>
