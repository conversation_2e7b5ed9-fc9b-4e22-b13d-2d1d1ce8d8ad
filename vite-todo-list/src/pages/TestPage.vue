<template>
  <div class="test-page">
    <n-card title="待办日历应用 - 测试页面">
      <div class="test-content">
        <n-alert type="info" title="测试账号信息" style="margin-bottom: 24px">
          <div class="test-accounts">
            <div><strong>普通用户:</strong> testuser / 123456</div>
            <div><strong>管理员:</strong> admin / 123456</div>
          </div>
        </n-alert>
        
        <n-space vertical size="large">
          <n-card title="功能特性" size="small">
            <ul>
              <li>📱 响应式设计 - 自动适配移动端和桌面端</li>
              <li>📅 日历功能 - VCalendar集成，事件颜色标识</li>
              <li>🍽️ 饮食规划 - 6种预设饮食类型</li>
              <li>📝 通用事件 - 记事本功能</li>
              <li>👤 用户系统 - 注册登录，个人资料</li>
              <li>🎯 事件管理 - 创建、编辑、删除、完成状态</li>
            </ul>
          </n-card>
          
          <n-card title="技术栈" size="small">
            <n-space>
              <n-tag type="primary">Vue 3</n-tag>
              <n-tag type="primary">TypeScript</n-tag>
              <n-tag type="success">NaiveUI</n-tag>
              <n-tag type="success">VCalendar</n-tag>
              <n-tag type="warning">Pinia</n-tag>
              <n-tag type="warning">Vue Router</n-tag>
              <n-tag type="info">Vite</n-tag>
            </n-space>
          </n-card>
          
          <n-space>
            <n-button type="primary" @click="$router.push('/login')">
              前往登录
            </n-button>
            <n-button @click="$router.push('/register')">
              前往注册
            </n-button>
          </n-space>
        </n-space>
      </div>
    </n-card>
  </div>
</template>

<script setup lang="ts">
// 测试页面逻辑
</script>

<style scoped>
.test-page {
  min-height: 100vh;
  padding: 24px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
}

.test-content {
  max-width: 600px;
}

.test-accounts {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.test-accounts div {
  font-family: monospace;
  font-size: 14px;
}

ul {
  margin: 0;
  padding-left: 20px;
}

li {
  margin: 8px 0;
  line-height: 1.5;
}

@media (max-width: 768px) {
  .test-page {
    padding: 16px;
  }
}
</style>
