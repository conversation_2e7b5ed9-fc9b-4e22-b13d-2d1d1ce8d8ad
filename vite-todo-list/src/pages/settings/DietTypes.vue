<template>
  <div class="diet-types-page">
    <div class="page-header">
      <n-button quaternary circle @click="$router.back()">
        <n-icon :size="20">
          <ArrowBack />
        </n-icon>
      </n-button>
      <h1>饮食类型管理</h1>
      <n-button type="primary" @click="showCreateModal = true">
        <n-icon :size="16">
          <Add />
        </n-icon>
        添加
      </n-button>
    </div>
    
    <div class="page-content">
      <!-- 系统预设类型 -->
      <n-card title="系统预设类型" class="diet-types-section">
        <div class="diet-types-list">
          <div
            v-for="dietType in systemDietTypes"
            :key="dietType.id"
            class="diet-type-item"
          >
            <div class="diet-type-info">
              <div class="diet-type-header">
                <h3>{{ dietType.name }}</h3>
                <n-tag :color="{ color: '#f0f0f0', textColor: '#666' }" size="small">
                  系统预设
                </n-tag>
              </div>
              <p class="diet-type-description">{{ dietType.description }}</p>
              <div class="diet-type-cycle">
                <n-icon :size="14" color="#666">
                  <Time />
                </n-icon>
                <span>{{ dietType.cycle_description }}</span>
              </div>
            </div>
            <div class="diet-type-actions">
              <n-button quaternary size="small" @click="viewDietType(dietType)">
                <n-icon :size="16">
                  <Eye />
                </n-icon>
              </n-button>
            </div>
          </div>
        </div>
      </n-card>
      
      <!-- 自定义类型 -->
      <n-card title="自定义类型" class="diet-types-section">
        <div v-if="customDietTypes.length === 0" class="empty-state">
          <n-icon :size="48" color="#ccc">
            <Restaurant />
          </n-icon>
          <p>还没有自定义饮食类型</p>
          <n-button type="primary" @click="showCreateModal = true">
            创建第一个自定义类型
          </n-button>
        </div>
        
        <div v-else class="diet-types-list">
          <div
            v-for="dietType in customDietTypes"
            :key="dietType.id"
            class="diet-type-item"
          >
            <div class="diet-type-info">
              <div class="diet-type-header">
                <h3>{{ dietType.name }}</h3>
                <n-tag :color="{ color: '#e6f7ff', textColor: '#1890ff' }" size="small">
                  自定义
                </n-tag>
              </div>
              <p class="diet-type-description">{{ dietType.description }}</p>
              <div class="diet-type-cycle">
                <n-icon :size="14" color="#666">
                  <Time />
                </n-icon>
                <span>{{ dietType.cycle_description }}</span>
              </div>
            </div>
            <div class="diet-type-actions">
              <n-button quaternary size="small" @click="viewDietType(dietType)">
                <n-icon :size="16">
                  <Eye />
                </n-icon>
              </n-button>
              <n-button quaternary size="small" @click="editDietType(dietType)">
                <n-icon :size="16">
                  <Create />
                </n-icon>
              </n-button>
              <n-button quaternary size="small" type="error" @click="deleteDietType(dietType)">
                <n-icon :size="16">
                  <Trash />
                </n-icon>
              </n-button>
            </div>
          </div>
        </div>
      </n-card>
    </div>
    
    <!-- 创建/编辑模态框 -->
    <n-modal v-model:show="showCreateModal" preset="card" title="创建饮食类型" style="width: 90%; max-width: 600px;">
      <n-form ref="formRef" :model="form" :rules="rules" label-placement="top">
        <n-form-item path="name" label="名称">
          <n-input v-model:value="form.name" placeholder="请输入饮食类型名称" />
        </n-form-item>
        
        <n-form-item path="description" label="描述">
          <n-input
            v-model:value="form.description"
            type="textarea"
            placeholder="请输入饮食类型描述"
            :rows="3"
          />
        </n-form-item>
        
        <n-form-item path="characteristics" label="特点">
          <n-input
            v-model:value="form.characteristics"
            type="textarea"
            placeholder="请输入饮食特点"
            :rows="2"
          />
        </n-form-item>
        
        <n-form-item path="benefits" label="益处">
          <n-input
            v-model:value="form.benefits"
            type="textarea"
            placeholder="请输入饮食益处"
            :rows="2"
          />
        </n-form-item>
        
        <n-form-item path="suitable_people" label="适合人群">
          <n-input
            v-model:value="form.suitable_people"
            placeholder="请输入适合人群"
          />
        </n-form-item>
        
        <n-form-item path="cycle_description" label="建议周期">
          <n-input
            v-model:value="form.cycle_description"
            placeholder="请输入建议的食用周期"
          />
        </n-form-item>
      </n-form>
      
      <template #footer>
        <div style="display: flex; justify-content: flex-end; gap: 12px;">
          <n-button @click="showCreateModal = false">取消</n-button>
          <n-button type="primary" @click="handleSubmit" :loading="loading">
            {{ editingDietType ? '更新' : '创建' }}
          </n-button>
        </div>
      </template>
    </n-modal>
    
    <!-- 查看详情模态框 -->
    <n-modal v-model:show="showViewModal" preset="card" title="饮食类型详情" style="width: 90%; max-width: 600px;">
      <div v-if="viewingDietType" class="diet-type-detail">
        <div class="detail-section">
          <h4>基本信息</h4>
          <p><strong>名称：</strong>{{ viewingDietType.name }}</p>
          <p><strong>英文名：</strong>{{ viewingDietType.name_en }}</p>
          <p><strong>描述：</strong>{{ viewingDietType.description }}</p>
        </div>
        
        <div class="detail-section">
          <h4>特点</h4>
          <p>{{ viewingDietType.characteristics }}</p>
        </div>
        
        <div class="detail-section">
          <h4>益处</h4>
          <p>{{ viewingDietType.benefits }}</p>
        </div>
        
        <div class="detail-section">
          <h4>适合人群</h4>
          <p>{{ viewingDietType.suitable_people }}</p>
        </div>
        
        <div class="detail-section">
          <h4>建议周期</h4>
          <p>{{ viewingDietType.cycle_description }}</p>
        </div>
        
        <div v-if="viewingDietType.food_types?.length" class="detail-section">
          <h4>主要食物类型</h4>
          <div class="food-types">
            <n-tag
              v-for="food in viewingDietType.food_types"
              :key="food"
              size="small"
              style="margin: 2px;"
            >
              {{ food }}
            </n-tag>
          </div>
        </div>
      </div>
    </n-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, reactive } from 'vue'
import { FormInst, FormRules } from 'naive-ui'
import {
  ArrowBack,
  Add,
  Eye,
  Create,
  Trash,
  Restaurant,
  Time
} from '@vicons/ionicons5'
import { useEventsStore } from '@/stores/events'
import type { DietType } from '@/types'

const eventsStore = useEventsStore()

// 响应式数据
const showCreateModal = ref(false)
const showViewModal = ref(false)
const loading = ref(false)
const editingDietType = ref<DietType | null>(null)
const viewingDietType = ref<DietType | null>(null)

// 表单数据
const form = reactive({
  name: '',
  description: '',
  characteristics: '',
  benefits: '',
  suitable_people: '',
  cycle_description: ''
})

// 表单验证规则
const rules: FormRules = {
  name: [
    { required: true, message: '请输入饮食类型名称', trigger: 'blur' }
  ],
  description: [
    { required: true, message: '请输入饮食类型描述', trigger: 'blur' }
  ]
}

const formRef = ref<FormInst | null>(null)

// 计算属性
const systemDietTypes = computed(() => {
  return eventsStore.dietTypes.filter(type => type.is_system)
})

const customDietTypes = computed(() => {
  return eventsStore.dietTypes.filter(type => !type.is_system)
})

// 方法
const viewDietType = (dietType: DietType) => {
  viewingDietType.value = dietType
  showViewModal.value = true
}

const editDietType = (dietType: DietType) => {
  editingDietType.value = dietType
  form.name = dietType.name
  form.description = dietType.description
  form.characteristics = dietType.characteristics
  form.benefits = dietType.benefits
  form.suitable_people = dietType.suitable_people
  form.cycle_description = dietType.cycle_description || ''
  showCreateModal.value = true
}

const deleteDietType = async (dietType: DietType) => {
  // TODO: 实现删除逻辑
  window.$message?.success('删除成功')
}

const handleSubmit = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    loading.value = true
    
    // TODO: 实现创建/更新逻辑
    
    window.$message?.success(editingDietType.value ? '更新成功' : '创建成功')
    showCreateModal.value = false
    resetForm()
  } catch (error) {
    console.error('Submit error:', error)
  } finally {
    loading.value = false
  }
}

const resetForm = () => {
  editingDietType.value = null
  form.name = ''
  form.description = ''
  form.characteristics = ''
  form.benefits = ''
  form.suitable_people = ''
  form.cycle_description = ''
}

// 初始化
onMounted(async () => {
  await eventsStore.init()
})
</script>

<style scoped>
.diet-types-page {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #f5f5f5;
}

.page-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  background: white;
  border-bottom: 1px solid #f0f0f0;
}

.page-header h1 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #333;
  flex: 1;
  text-align: center;
}

.page-content {
  flex: 1;
  padding: 16px;
  display: flex;
  flex-direction: column;
  gap: 16px;
  overflow-y: auto;
}

.diet-types-section {
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.diet-types-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.diet-type-item {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  padding: 16px;
  background: #fafafa;
  border-radius: 8px;
  border: 1px solid #f0f0f0;
}

.diet-type-info {
  flex: 1;
}

.diet-type-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.diet-type-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.diet-type-description {
  margin: 0 0 8px 0;
  color: #666;
  font-size: 14px;
  line-height: 1.4;
}

.diet-type-cycle {
  display: flex;
  align-items: center;
  gap: 4px;
  color: #999;
  font-size: 12px;
}

.diet-type-actions {
  display: flex;
  gap: 4px;
  margin-left: 12px;
}

.empty-state {
  text-align: center;
  padding: 40px 20px;
  color: #666;
}

.empty-state p {
  margin: 16px 0;
  font-size: 14px;
}

.diet-type-detail {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.detail-section h4 {
  margin: 0 0 8px 0;
  font-size: 14px;
  font-weight: 600;
  color: #333;
}

.detail-section p {
  margin: 0;
  color: #666;
  font-size: 14px;
  line-height: 1.5;
}

.food-types {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

/* 桌面端样式 */
@media (min-width: 768px) {
  .page-content {
    max-width: 800px;
    margin: 0 auto;
    padding: 24px;
  }
  
  .page-header h1 {
    font-size: 20px;
  }
}
</style>
