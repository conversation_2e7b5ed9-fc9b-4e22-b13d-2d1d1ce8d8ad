<template>
  <div class="event-detail-page">
    <div class="page-header">
      <n-button quaternary @click="$router.back()">
        <n-icon :size="20">
          <ArrowBack />
        </n-icon>
      </n-button>
      <h1>事件详情</h1>
      <n-dropdown :options="actionOptions" @select="handleActionSelect">
        <n-button quaternary>
          <n-icon :size="20">
            <EllipsisVertical />
          </n-icon>
        </n-button>
      </n-dropdown>
    </div>
    
    <div class="page-content">
      <n-spin :show="loading">
        <div v-if="event" class="event-detail">
          <div class="event-header">
            <div class="event-type-indicator" :style="{ backgroundColor: eventColor }"></div>
            <div class="event-info">
              <h2 class="event-title">{{ event.title }}</h2>
              <div class="event-type">{{ eventType?.name }}</div>
            </div>
            <n-button
              :type="event.is_completed ? 'success' : 'default'"
              @click="toggleCompletion"
            >
              <n-icon :size="16">
                <Checkmark v-if="event.is_completed" />
                <Ellipse v-else />
              </n-icon>
              {{ event.is_completed ? '已完成' : '标记完成' }}
            </n-button>
          </div>
          
          <div class="event-content">
            <div class="info-section">
              <h3>基本信息</h3>
              <div class="info-grid">
                <div class="info-item">
                  <label>开始日期</label>
                  <span>{{ formatDate(event.start_date, 'YYYY年MM月DD日') }}</span>
                </div>
                <div v-if="event.start_time" class="info-item">
                  <label>开始时间</label>
                  <span>{{ formatTime(event.start_time) }}</span>
                </div>
                <div class="info-item">
                  <label>优先级</label>
                  <n-tag :type="getPriorityType(event.priority)">
                    {{ getPriorityLabel(event.priority) }}
                  </n-tag>
                </div>
                <div class="info-item">
                  <label>创建时间</label>
                  <span>{{ formatDate(event.created_at, 'YYYY-MM-DD HH:mm') }}</span>
                </div>
              </div>
            </div>
            
            <div v-if="event.description" class="info-section">
              <h3>描述</h3>
              <p class="description">{{ event.description }}</p>
            </div>
            
            <!-- 动态字段显示 -->
            <div v-if="hasExtraData" class="info-section">
              <h3>详细信息</h3>
              <div class="extra-data">
                <div
                  v-for="(value, key) in event.extra_data"
                  :key="key"
                  class="extra-item"
                >
                  <label>{{ getFieldLabel(key) }}</label>
                  <span>{{ formatFieldValue(key, value) }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <div v-else class="not-found">
          <n-icon :size="64" color="#ccc">
            <Search />
          </n-icon>
          <p>事件不存在</p>
        </div>
      </n-spin>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, h } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import {
  ArrowBack,
  EllipsisVertical,
  Checkmark,
  Ellipse,
  Search,
  Create,
  Trash
} from '@vicons/ionicons5'
import type { DropdownOption } from 'naive-ui'
import { useEventsStore } from '@/stores/events'
import { formatDate, formatTime } from '@/utils/date'
import type { Event } from '@/types'

const route = useRoute()
const router = useRouter()
const eventsStore = useEventsStore()

// 状态
const loading = ref(false)
const event = ref<Event | null>(null)

// 事件ID
const eventId = computed(() => Number(route.params.id))

// 事件类型
const eventType = computed(() => {
  return event.value ? eventsStore.getEventType(event.value.event_type_id) : null
})

// 事件颜色
const eventColor = computed(() => {
  return eventType.value?.color || '#2196F3'
})

// 是否有额外数据
const hasExtraData = computed(() => {
  return event.value?.extra_data && Object.keys(event.value.extra_data).length > 0
})

// 操作选项
const actionOptions: DropdownOption[] = [
  {
    label: '编辑',
    key: 'edit',
    icon: () => h(Create)
  },
  {
    label: '删除',
    key: 'delete',
    icon: () => h(Trash)
  }
]

// 获取优先级类型
const getPriorityType = (priority: string) => {
  const typeMap: Record<string, any> = {
    low: 'info',
    medium: 'warning',
    high: 'error'
  }
  return typeMap[priority] || 'default'
}

// 获取优先级标签
const getPriorityLabel = (priority: string) => {
  const labelMap: Record<string, string> = {
    low: '低',
    medium: '中',
    high: '高'
  }
  return labelMap[priority] || priority
}

// 获取字段标签
const getFieldLabel = (fieldName: string) => {
  if (!eventType.value?.form_config) return fieldName
  
  const field = eventType.value.form_config.fields.find((f: any) => f.name === fieldName)
  return field?.label || fieldName
}

// 格式化字段值
const formatFieldValue = (_fieldName: string, value: any) => {
  if (value === null || value === undefined) return '-'
  if (typeof value === 'object') return JSON.stringify(value)
  return String(value)
}

// 切换完成状态
const toggleCompletion = async () => {
  if (!event.value) return
  
  const result = await eventsStore.toggleEventCompletion(event.value.id, !event.value.is_completed)
  if (result) {
    event.value = result
  }
}

// 处理操作选择
const handleActionSelect = (key: string) => {
  switch (key) {
    case 'edit':
      router.push(`/events/${eventId.value}/edit`)
      break
    case 'delete':
      handleDelete()
      break
  }
}

// 处理删除
const handleDelete = () => {
  console.log('🗑️ 准备删除事件:', eventId.value, event.value?.title)

  window.$dialog?.warning({
    title: '确认删除',
    content: '确定要删除这个事件吗？此操作不可撤销。',
    positiveText: '删除',
    negativeText: '取消',
    onPositiveClick: async () => {
      console.log('🗑️ 用户确认删除事件:', eventId.value)
      const success = await eventsStore.deleteEvent(eventId.value)
      console.log('🗑️ 删除结果:', success)
      if (success) {
        console.log('🗑️ 删除成功，返回上一页')
        router.back()
      } else {
        console.error('🗑️ 删除失败')
      }
    }
  })
}

// 获取事件详情
const fetchEventDetail = async () => {
  loading.value = true
  try {
    // 这里应该调用API获取事件详情
    // 暂时从store中查找
    const foundEvent = eventsStore.events.find((e: any) => e.id === eventId.value)
    event.value = foundEvent as Event || null
  } catch (error) {
    console.error('Fetch event detail error:', error)
  } finally {
    loading.value = false
  }
}

// 初始化
onMounted(async () => {
  await eventsStore.init()
  await fetchEventDetail()
})
</script>

<style scoped>
.event-detail-page {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.page-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 24px;
  border-bottom: 1px solid #e0e0e0;
  background: white;
}

.page-header h1 {
  font-size: 20px;
  font-weight: 600;
  color: #333;
}

.page-content {
  flex: 1;
  padding: 24px;
  overflow-y: auto;
  background: #f5f5f5;
}

.event-detail {
  max-width: 800px;
  margin: 0 auto;
}

.event-header {
  background: white;
  padding: 24px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 24px;
  display: flex;
  align-items: center;
  gap: 16px;
}

.event-type-indicator {
  width: 6px;
  height: 60px;
  border-radius: 3px;
}

.event-info {
  flex: 1;
}

.event-title {
  font-size: 24px;
  font-weight: 600;
  color: #333;
  margin-bottom: 8px;
}

.event-type {
  font-size: 14px;
  color: #666;
}

.event-content {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.info-section {
  background: white;
  padding: 24px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.info-section h3 {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin-bottom: 16px;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.info-item label {
  font-size: 14px;
  color: #666;
  font-weight: 500;
}

.info-item span {
  font-size: 14px;
  color: #333;
}

.description {
  font-size: 14px;
  color: #333;
  line-height: 1.6;
  white-space: pre-wrap;
}

.extra-data {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.extra-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.extra-item:last-child {
  border-bottom: none;
}

.extra-item label {
  font-size: 14px;
  color: #666;
  font-weight: 500;
}

.extra-item span {
  font-size: 14px;
  color: #333;
}

.not-found {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
}

.not-found p {
  margin-top: 16px;
  color: #666;
  font-size: 16px;
}

@media (max-width: 768px) {
  .page-header {
    padding: 12px 16px;
  }
  
  .page-content {
    padding: 16px;
  }
  
  .event-header {
    padding: 16px;
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
  
  .info-section {
    padding: 16px;
  }
  
  .info-grid {
    grid-template-columns: 1fr;
  }
}
</style>
