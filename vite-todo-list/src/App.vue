<template>
  <n-config-provider :theme="theme">
    <n-loading-bar-provider>
      <n-dialog-provider>
        <n-notification-provider>
          <n-message-provider>
            <GlobalProviders />
            <AppContent />
            <n-global-style />
          </n-message-provider>
        </n-notification-provider>
      </n-dialog-provider>
    </n-loading-bar-provider>
  </n-config-provider>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import AppContent from './components/AppContent.vue'
import GlobalProviders from './components/GlobalProviders.vue'

// 主题配置（暂时使用浅色主题）
const theme = ref(null)
</script>

<style>
/* 全局样式已移至 assets/styles/main.css */
</style>
