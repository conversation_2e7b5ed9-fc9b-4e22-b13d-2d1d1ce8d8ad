// 用户相关类型
export interface User {
  id: number
  username: string
  email: string
  nickname?: string
  avatar_url?: string
  created_at: string
  updated_at: string
  is_active: boolean
}

export interface LoginForm {
  username: string
  password: string
}

export interface RegisterForm {
  username: string
  email: string
  password: string
  nickname?: string
}

// 饮食类型
export interface DietType {
  id: number
  name: string
  name_en: string
  description: string
  food_types: string[]
  nutrition_info: Record<string, string>
  characteristics: string
  benefits: string
  suitable_people: string
  // 建议食用周期相关字段
  suggested_frequency?: 'daily' | 'weekly' | 'monthly'
  suggested_interval?: number
  suggested_duration_days?: number
  suggested_frequency_per_week?: number
  cycle_description?: string
  is_system?: boolean
  created_at: string
}

// 事件类型
export interface EventType {
  id: number
  name: string
  name_en: string
  description: string
  icon: string
  color: string
  form_config: FormConfig
  is_system: boolean
  created_at: string
}

export interface FormConfig {
  fields: FormField[]
}

export interface FormField {
  name: string
  type: 'text' | 'textarea' | 'number' | 'select' | 'datetime' | 'date' | 'time'
  label: string
  required?: boolean
  options?: string[]
  default?: any
}

// 事件相关类型
export interface Event {
  id: number
  user_id: number
  event_type_id: number
  title: string
  description?: string
  start_date: string
  end_date?: string
  start_time?: string
  end_time?: string
  duration_type: 'single' | 'daily' | 'weekly' | 'monthly'
  duration_value: number
  recurrence_pattern?: RecurrencePattern
  is_completed: boolean
  completed_at?: string
  priority: 'low' | 'medium' | 'high'
  extra_data?: Record<string, any>
  created_at: string
  updated_at: string
  event_type?: EventType
}

export interface RecurrencePattern {
  frequency: 'daily' | 'weekly' | 'monthly'
  interval: number
  end_date?: string
  count?: number
  days_of_week?: number[] // 0-6, 0 = Sunday
}

export interface CreateEventForm {
  event_type_id: number
  title: string
  description?: string
  start_date: string
  end_date?: string
  start_time?: string
  end_time?: string
  duration_type: 'single' | 'daily' | 'weekly' | 'monthly'
  duration_value: number
  recurrence_pattern?: RecurrencePattern
  priority: 'low' | 'medium' | 'high'
  extra_data?: Record<string, any>
}

// 饮食事件详情
export interface DietEvent {
  id: number
  event_id: number
  diet_type_id?: number
  meal_type: 'breakfast' | 'lunch' | 'dinner' | 'snack'
  planned_foods?: string[]
  actual_foods?: string[]
  calories_planned?: number
  calories_actual?: number
  notes?: string
  created_at: string
  updated_at: string
  diet_type?: DietType
}

// 事件完成记录
export interface EventCompletion {
  id: number
  event_id: number
  completion_date: string
  completed_at: string
  notes?: string
}

// 设备类型
export type DeviceType = 'mobile' | 'desktop'

// API响应类型
export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  message?: string
  error?: string
}

// 分页类型
export interface PaginationParams {
  page: number
  limit: number
}

export interface PaginatedResponse<T> {
  data: T[]
  total: number
  page: number
  limit: number
  total_pages: number
}

// 日历相关类型
export interface CalendarEvent {
  id: string
  title: string
  start: Date
  end?: Date
  color: string
  isCompleted: boolean
  event: Event
}

// 导航菜单项
export interface NavItem {
  name: string
  icon: string
  path: string
  label: string
}
