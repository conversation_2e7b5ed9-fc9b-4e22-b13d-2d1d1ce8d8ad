import type { DeviceType } from '@/types'

/**
 * 检测设备类型
 */
export function detectDeviceType(): DeviceType {
  const userAgent = navigator.userAgent.toLowerCase()
  const isMobile = /android|webos|iphone|ipad|ipod|blackberry|iemobile|opera mini/i.test(userAgent)
  
  // 也可以通过屏幕尺寸判断
  const isSmallScreen = window.innerWidth <= 768
  
  return isMobile || isSmallScreen ? 'mobile' : 'desktop'
}

/**
 * 监听设备类型变化
 */
export function useDeviceType() {
  const deviceType = ref<DeviceType>(detectDeviceType())
  
  const updateDeviceType = () => {
    deviceType.value = detectDeviceType()
  }
  
  onMounted(() => {
    window.addEventListener('resize', updateDeviceType)
  })
  
  onUnmounted(() => {
    window.removeEventListener('resize', updateDeviceType)
  })
  
  return {
    deviceType: readonly(deviceType),
    isMobile: computed(() => deviceType.value === 'mobile'),
    isDesktop: computed(() => deviceType.value === 'desktop')
  }
}
