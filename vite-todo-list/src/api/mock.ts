// 模拟API数据，用于开发测试
import type { User, EventType, DietType, Event } from '@/types'

// 测试用户数据
export const mockUsers: User[] = [
  {
    id: 1,
    username: 'testuser',
    email: '<EMAIL>',
    nickname: '测试用户',
    avatar_url: '',
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z',
    is_active: true
  },
  {
    id: 2,
    username: 'admin',
    email: '<EMAIL>',
    nickname: '管理员',
    avatar_url: '',
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z',
    is_active: true
  }
]

// 测试账号密码
export const mockCredentials = {
  'testuser': '123456',
  'admin': '123456'
}

// 模拟事件类型数据
export const mockEventTypes: EventType[] = [
  {
    id: 1,
    name: '饮食规划',
    name_en: 'Diet Planning',
    description: '记录和规划日常饮食',
    icon: 'restaurant',
    color: '#4CAF50',
    form_config: {
      fields: [
        { name: 'diet_type', type: 'select', label: '饮食类型', required: true },
        { name: 'meal_type', type: 'select', label: '餐次', options: ['breakfast', 'lunch', 'dinner', 'snack'], required: true },
        { name: 'planned_foods', type: 'textarea', label: '计划食物' },
        { name: 'calories_planned', type: 'number', label: '计划卡路里' }
      ]
    },
    is_system: true,
    created_at: '2024-01-01T00:00:00Z'
  },
  {
    id: 2,
    name: '通用事件',
    name_en: 'General Event',
    description: '通用的记事本功能',
    icon: 'create',
    color: '#2196F3',
    form_config: {
      fields: [
        { name: 'content', type: 'textarea', label: '内容', required: true },
        { name: 'priority', type: 'select', label: '优先级', options: ['low', 'medium', 'high'], default: 'medium' }
      ]
    },
    is_system: true,
    created_at: '2024-01-01T00:00:00Z'
  }
]

// 模拟饮食类型数据
export const mockDietTypes: DietType[] = [
  {
    id: 1,
    name: '地中海饮食',
    name_en: 'Mediterranean Diet',
    description: '以蔬菜、水果、全谷物、豆类、坚果、橄榄油、鱼类和适量红酒为主，减少红肉和加工食品摄入的健康饮食模式',
    food_types: ['橄榄油', '鱼类', '坚果', '全谷物', '蔬菜', '水果', '豆类', '适量红酒'],
    nutrition_info: { 'omega3': '丰富', '纤维': '高', '抗氧化物': '丰富', '饱和脂肪': '低', '单不饱和脂肪': '高' },
    characteristics: '以蔬菜、水果、全谷物、豆类、坚果、橄榄油、鱼类和适量红酒为主，减少红肉和加工食品摄入。',
    benefits: '对心血管健康有益，降低心脏病、中风和某些癌症风险，改善认知功能。',
    suitable_people: '追求长期健康、喜欢清淡口味的人群。',
    suggested_frequency: 'weekly' as const,
    suggested_interval: 1,
    suggested_frequency_per_week: 5, // 每周5天
    cycle_description: '建议每周5天采用地中海饮食，周末可适当放松',
    is_system: true,
    created_at: '2024-01-01T00:00:00Z'
  },
  {
    id: 2,
    name: 'DASH饮食',
    name_en: 'DASH Diet',
    description: '膳食方式预防高血压，强调低钠、高钾、镁、钙的食物',
    food_types: ['低脂乳制品', '全谷物', '蔬菜', '水果', '瘦肉', '鱼类', '坚果', '豆类'],
    nutrition_info: { '钠': '低', '钾': '高', '钙': '高', '镁': '高', '纤维': '高' },
    characteristics: '强调低钠、高钾、镁、钙的食物，如蔬菜、水果、全谷物、低脂乳制品、瘦肉和鱼类，限制高脂肪和高糖食品。',
    benefits: '有效降低血压，改善心血管健康，适合控制体重。',
    suitable_people: '高血压患者或有心血管疾病风险的人。',
    suggested_frequency: 'weekly' as const,
    suggested_interval: 1,
    suggested_frequency_per_week: 6, // 每周6天
    cycle_description: '建议每周6天严格遵循DASH饮食原则，每周休息1天',
    is_system: true,
    created_at: '2024-01-01T00:00:00Z'
  },
  {
    id: 3,
    name: '生酮饮食',
    name_en: 'Ketogenic Diet',
    description: '极低碳水化合物、高脂肪的饮食方式，促使身体进入酮症状态',
    food_types: ['肉类', '鱼类', '蛋类', '奶酪', '坚果', '种子', '低碳蔬菜', '健康油脂'],
    nutrition_info: { '碳水化合物': '极低(5-10%)', '脂肪': '高(70-80%)', '蛋白质': '中等(15-25%)' },
    characteristics: '严格限制碳水化合物摄入，大幅增加脂肪比例，适量蛋白质。',
    benefits: '快速减重，改善血糖控制，可能提高认知功能。',
    suitable_people: '需要快速减重的人群，糖尿病患者（需医生指导）。',
    suggested_frequency: 'monthly' as const,
    suggested_interval: 1,
    suggested_frequency_per_week: 2, // 每周2次
    cycle_description: '建议每周进行2次生酮饮食，其他时间正常饮食，需医生指导',
    is_system: true,
    created_at: '2024-01-01T00:00:00Z'
  },
  {
    id: 4,
    name: '间歇性断食',
    name_en: 'Intermittent Fasting',
    description: '通过控制进食时间窗口来调节新陈代谢的饮食方式',
    food_types: ['进食窗口内正常饮食', '断食期间只喝水、茶、黑咖啡'],
    nutrition_info: { '热量控制': '自然限制', '胰岛素敏感性': '提高', '代谢灵活性': '增强' },
    characteristics: '16:8、18:6或5:2等不同模式，在特定时间窗口内进食。',
    benefits: '体重管理，改善胰岛素敏感性，可能延缓衰老。',
    suitable_people: '健康成年人，需要体重管理的人群。',
    suggested_frequency: 'weekly' as const,
    suggested_interval: 1,
    suggested_frequency_per_week: 5, // 每周5天
    cycle_description: '建议每周5天进行16:8间歇性断食，周末可正常饮食',
    is_system: true,
    created_at: '2024-01-01T00:00:00Z'
  },
  {
    id: 5,
    name: '传统中式饮食',
    name_en: 'Traditional Chinese Diet',
    description: '基于中医理论的传统中式饮食，讲究食物搭配和药食同源',
    food_types: ['谷物', '蔬菜', '豆类', '少量肉类', '中药材', '茶类'],
    nutrition_info: { '平衡': '阴阳调和', '五味': '酸甜苦辣咸', '温热寒凉': '因人而异' },
    characteristics: '讲究食物搭配，注重营养平衡和药食同源。',
    benefits: '调理体质，预防疾病，促进消化。',
    suitable_people: '注重养生的人群，体质需要调理的人群。',
    suggested_frequency: 'weekly' as const,
    suggested_interval: 1,
    suggested_frequency_per_week: 4, // 每周4天
    cycle_description: '建议每周4天采用传统中式饮食，其他时间可适当调整',
    is_system: true,
    created_at: '2024-01-01T00:00:00Z'
  }
]

// 模拟事件数据
export const mockEvents: Event[] = [
  {
    id: 1,
    user_id: 1,
    event_type_id: 1,
    title: '今日早餐规划',
    description: '健康营养的早餐搭配',
    start_date: new Date().toISOString().split('T')[0],
    end_date: undefined,
    start_time: '08:00:00',
    end_time: '09:00:00',
    duration_type: 'single',
    duration_value: 1,
    recurrence_pattern: undefined,
    is_completed: false,
    completed_at: undefined,
    priority: 'medium',
    extra_data: {
      diet_type: 1,
      meal_type: 'breakfast',
      planned_foods: ['燕麦', '牛奶', '香蕉', '坚果'],
      calories_planned: 350
    },
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z'
  },
  {
    id: 2,
    user_id: 1,
    event_type_id: 2,
    title: '工作计划',
    description: '完成项目开发任务',
    start_date: new Date().toISOString().split('T')[0],
    end_date: undefined,
    start_time: '09:00:00',
    end_time: '17:00:00',
    duration_type: 'single',
    duration_value: 1,
    recurrence_pattern: undefined,
    is_completed: false,
    completed_at: undefined,
    priority: 'high',
    extra_data: {
      content: '完成待办日历应用的开发和测试',
      priority: 'high'
    },
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z'
  },
  {
    id: 3,
    user_id: 1,
    event_type_id: 3,
    title: '晨跑锻炼',
    description: '在公园进行30分钟晨跑',
    start_date: new Date().toISOString().split('T')[0],
    end_date: undefined,
    start_time: '06:30:00',
    end_time: '07:00:00',
    duration_type: 'single',
    duration_value: 1,
    recurrence_pattern: {
      frequency: 'daily',
      interval: 1,
      count: undefined,
      end_date: undefined
    },
    is_completed: false,
    completed_at: undefined,
    priority: 'medium',
    extra_data: {
      exercise_type: 'running',
      duration_minutes: 30,
      location: '中央公园'
    },
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z'
  },
  {
    id: 4,
    user_id: 1,
    event_type_id: 1,
    title: '午餐时间',
    description: '健康午餐搭配',
    start_date: new Date().toISOString().split('T')[0],
    end_date: undefined,
    start_time: '12:00:00',
    end_time: '13:00:00',
    duration_type: 'single',
    duration_value: 1,
    recurrence_pattern: undefined,
    is_completed: true,
    completed_at: new Date().toISOString(),
    priority: 'medium',
    extra_data: {
      diet_type: 2,
      meal_type: 'lunch',
      planned_foods: ['鸡胸肉', '蔬菜沙拉', '糙米饭', '水果'],
      calories_planned: 450
    },
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z'
  },
  {
    id: 5,
    user_id: 1,
    event_type_id: 4,
    title: '团队会议',
    description: '项目进度讨论会议',
    start_date: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString().split('T')[0], // 明天
    end_date: undefined,
    start_time: '14:00:00',
    end_time: '15:30:00',
    duration_type: 'single',
    duration_value: 1,
    recurrence_pattern: undefined,
    is_completed: false,
    completed_at: undefined,
    priority: 'high',
    extra_data: {
      meeting_type: 'team_meeting',
      location: '会议室A',
      attendees: ['张三', '李四', '王五']
    },
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z'
  },
  {
    id: 6,
    user_id: 1,
    event_type_id: 5,
    title: '读书时间',
    description: '阅读技术书籍',
    start_date: new Date().toISOString().split('T')[0],
    end_date: undefined,
    start_time: '20:00:00',
    end_time: '21:00:00',
    duration_type: 'single',
    duration_value: 1,
    recurrence_pattern: {
      frequency: 'weekly',
      interval: 1,
      count: undefined,
      end_date: undefined
    },
    is_completed: false,
    completed_at: undefined,
    priority: 'low',
    extra_data: {
      book_title: 'Vue.js设计与实现',
      pages_target: 30
    },
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z'
  }
]

// 模拟API延迟
export const mockDelay = (ms: number = 500) => new Promise(resolve => setTimeout(resolve, ms))

// 模拟登录验证
export const mockLogin = async (username: string, password: string) => {
  await mockDelay(1000) // 模拟网络延迟
  
  if (mockCredentials[username as keyof typeof mockCredentials] === password) {
    const user = mockUsers.find(u => u.username === username)
    if (user) {
      return {
        success: true,
        data: {
          user,
          token: `mock_token_${user.id}_${Date.now()}`
        }
      }
    }
  }
  
  throw new Error('用户名或密码错误')
}
